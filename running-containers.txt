CONTAINER ID   IMAGE                  COMMAND                  CREATED        STATUS                          PORTS                                         NAMES
e1695724d121   archon-backend         "uvicorn main:app --…"   13 hours ago   Up 16 minutes (healthy)         0.0.0.0:8000->8000/tcp, [::]:8000->8000/tcp   archon-backend
821890af1401   archon-celery-worker   "celery -A app.libs.…"   13 hours ago   Restarting (2) 23 seconds ago                                                 archon-celery-worker
948b844eb786   b1fd7ad9d178           "docker-entrypoint.s…"   29 hours ago   Up 16 minutes (unhealthy)       0.0.0.0:5173->5173/tcp, [::]:5173->5173/tcp   archon-frontend
32960b5ca9ff   postgres:15            "docker-entrypoint.s…"   29 hours ago   Up 16 minutes (healthy)         0.0.0.0:5432->5432/tcp, [::]:5432->5432/tcp   archon-postgres
8a588463e172   redis:7-alpine         "docker-entrypoint.s…"   29 hours ago   Up 16 minutes (healthy)         0.0.0.0:6379->6379/tcp, [::]:6379->6379/tcp   archon-redis
