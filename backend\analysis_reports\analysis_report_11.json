{"overall_score": 62.0, "structure_score": 100.0, "quality_score": 0, "security_score": 60.0, "dependencies_score": 100.0, "issues": [{"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F401: `schemas.game.NarratorFinishedPayload` imported but unused", "description": "`schemas.game.NarratorFinishedPayload` imported but unused | Rule: F401 | Columns: 5-28 | Fix: Remove unused import: `schemas.game.NarratorFinishedPayload`", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 24, "start_line": 24, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TD002: Missing author in TODO; try: `# TODO(<author_name>): ...` or `# TODO @<author_name>: ...`", "description": "Missing author in TODO; try: `# TODO(<author_name>): ...` or `# TODO @<author_name>: ...` | Rule: TD002 | Columns: 3-7", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 60, "start_line": 60, "start_column": 3}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TD003: Missing issue link on the line following this TODO", "description": "Missing issue link on the line following this TODO | Rule: TD003 | Columns: 3-7", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 60, "start_line": 60, "start_column": 3}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FIX002: Line contains TODO, consider resolving the issue", "description": "Line contains TODO, consider resolving the issue | Rule: FIX002 | Columns: 3-7", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 60, "start_line": 60, "start_column": 3}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR0913: Too many arguments in function definition (9 > 5)", "description": "Too many arguments in function definition (9 > 5) | Rule: PLR0913 | Columns: 9-17", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 62, "start_line": 62, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 23-27", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 90, "start_line": 90, "start_column": 23}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "C408: Unnecessary `dict` call (rewrite as a literal)", "description": "Unnecessary `dict` call (rewrite as a literal) | Rule: C408 | Columns: 50-56 | Fix: Rewrite as a literal", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 90, "start_line": 90, "start_column": 50}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 28-32", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 91, "start_line": 91, "start_column": 28}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "C408: Unnecessary `dict` call (rewrite as a literal)", "description": "Unnecessary `dict` call (rewrite as a literal) | Rule: C408 | Columns: 45-51 | Fix: Rewrite as a literal", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 91, "start_line": 91, "start_column": 45}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 26-30", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 96, "start_line": 96, "start_column": 26}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 26-30", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 97, "start_line": 97, "start_column": 26}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.List`", "description": "Missing `from __future__ import annotations`, but uses `typing.List` | Rule: FA100 | Columns: 25-29", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 98, "start_line": 98, "start_column": 25}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-59 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 107, "start_line": 107, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-59 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 107, "start_line": 107, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 17-71", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 114, "start_line": 114, "start_column": 17}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 35-70", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 114, "start_line": 114, "start_column": 35}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-55 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 117, "start_line": 117, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-55 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 117, "start_line": 117, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PERF203: `try`-`except` within a loop incurs performance overhead", "description": "`try`-`except` within a loop incurs performance overhead | Rule: PERF203 | Columns: 13-41", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 122, "start_line": 122, "start_column": 13}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 20-29", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 122, "start_line": 122, "start_column": 20}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 17-64", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 123, "start_line": 123, "start_column": 17}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 35-63", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 123, "start_line": 123, "start_column": 35}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 30-94", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 133, "start_line": 133, "start_column": 30}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 34-98", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 143, "start_line": 143, "start_column": 34}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-64 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 149, "start_line": 149, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-64 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 149, "start_line": 149, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-145", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 159, "start_line": 159, "start_column": 26}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (146 > 120)", "description": "Line too long (146 > 120) | Rule: E501 | Columns: 120-146", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 159, "start_line": 159, "start_column": 120}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-63 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 163, "start_line": 163, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-63 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 163, "start_line": 163, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-144", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 173, "start_line": 173, "start_column": 26}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (145 > 120)", "description": "Line too long (145 > 120) | Rule: E501 | Columns: 120-145", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 173, "start_line": 173, "start_column": 120}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-76 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 177, "start_line": 177, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-76 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 177, "start_line": 177, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 26-83 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 183, "start_line": 183, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-83", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 183, "start_line": 183, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-115", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 194, "start_line": 194, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-67", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 200, "start_line": 200, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 35-87", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 204, "start_line": 204, "start_column": 35}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 34-82 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 206, "start_line": 206, "start_column": 34}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 34-82", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 206, "start_line": 206, "start_column": 34}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-69 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 211, "start_line": 211, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-69 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 211, "start_line": 211, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-78", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 212, "start_line": 212, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-108", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 216, "start_line": 216, "start_column": 26}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 26-94 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 219, "start_line": 219, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-94", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 219, "start_line": 219, "start_column": 26}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 20-29", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 228, "start_line": 228, "start_column": 20}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (145 > 120)", "description": "Line too long (145 > 120) | Rule: E501 | Columns: 121-146", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 229, "start_line": 229, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-5 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 233, "start_line": 233, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 237, "start_line": 237, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 254, "start_line": 254, "start_column": 16}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 30-161", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 327, "start_line": 327, "start_column": 30}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (161 > 120)", "description": "Line too long (161 > 120) | Rule: E501 | Columns: 121-162", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 327, "start_line": 327, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF015: Prefer `next(iter(self.heal_votes.values()))` over single element slice", "description": "Prefer `next(iter(self.heal_votes.values()))` over single element slice | Rule: RUF015 | Columns: 16-49 | Fix: Replace with `next(iter(self.heal_votes.values()))`", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 361, "start_line": 361, "start_column": 16}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PERF102: When using only the values of a dict use the `values()` method", "description": "When using only the values of a dict use the `values()` method | Rule: PERF102 | Columns: 26-47 | Fix: Replace `.items()` with `.values()`", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 367, "start_line": 367, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET505: Unnecessary `else` after `return` statement", "description": "Unnecessary `else` after `return` statement | Rule: RET505 | Columns: 9-13", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 373, "start_line": 373, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET505: Unnecessary `elif` after `return` statement", "description": "Unnecessary `elif` after `return` statement | Rule: RET505 | Columns: 9-13", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 379, "start_line": 379, "start_column": 9}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SIM108: Use ternary operator `visible_votes = self.heal_votes if self.game_state.phase == Phase.NIGHT else self.cast_votes` instead of `if`-`else`-block", "description": "Use ternary operator `visible_votes = self.heal_votes if self.game_state.phase == Phase.NIGHT else self.cast_votes` instead of `if`-`else`-block | Rule: SIM108 | Columns: 13-48 | Fix: Replace `if`-`else`-block with `visible_votes = self.heal_votes if self.game_state.phase == Phase.NIGHT else self.cast_votes`", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 395, "start_line": 395, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR5501: Use `elif` instead of `else` then `if`, to reduce indentation", "description": "Use `elif` instead of `else` then `if`, to reduce indentation | Rule: PLR5501 | Columns: 9-13", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 399, "start_line": 399, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (122 > 120)", "description": "Line too long (122 > 120) | Rule: E501 | Columns: 121-123", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 460, "start_line": 460, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (141 > 120)", "description": "Line too long (141 > 120) | Rule: E501 | Columns: 121-142", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 462, "start_line": 462, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-72", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 470, "start_line": 470, "start_column": 26}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SIM118: Use `key in dict` instead of `key in dict.keys()`", "description": "Use `key in dict` instead of `key in dict.keys()` | Rule: SIM118 | Columns: 13-53 | Fix: Remove `.keys()`", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 479, "start_line": 479, "start_column": 13}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (151 > 120)", "description": "Line too long (151 > 120) | Rule: E501 | Columns: 121-152", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 491, "start_line": 491, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (122 > 120)", "description": "Line too long (122 > 120) | Rule: E501 | Columns: 121-123", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 503, "start_line": 503, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (141 > 120)", "description": "Line too long (141 > 120) | Rule: E501 | Columns: 121-142", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 505, "start_line": 505, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 20-29", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 519, "start_line": 519, "start_column": 20}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 17-78", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 520, "start_line": 520, "start_column": 17}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 35-77", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 520, "start_line": 520, "start_column": 35}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (141 > 120)", "description": "Line too long (141 > 120) | Rule: E501 | Columns: 121-142", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 522, "start_line": 522, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 20-29", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 537, "start_line": 537, "start_column": 20}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 17-77", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 538, "start_line": 538, "start_column": 17}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 35-76", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 538, "start_line": 538, "start_column": 35}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (124 > 120)", "description": "Line too long (124 > 120) | Rule: E501 | Columns: 121-125", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 540, "start_line": 540, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 562, "start_line": 562, "start_column": 16}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (127 > 120)", "description": "Line too long (127 > 120) | Rule: E501 | Columns: 121-128", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 563, "start_line": 563, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 587, "start_line": 587, "start_column": 16}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (194 > 120)", "description": "Line too long (194 > 120) | Rule: E501 | Columns: 121-195", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 588, "start_line": 588, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 20-29", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 602, "start_line": 602, "start_column": 20}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (135 > 120)", "description": "Line too long (135 > 120) | Rule: E501 | Columns: 121-136", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 604, "start_line": 604, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (122 > 120)", "description": "Line too long (122 > 120) | Rule: E501 | Columns: 121-123", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 639, "start_line": 639, "start_column": 121}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SIM118: Use `key in dict` instead of `key in dict.keys()`", "description": "Use `key in dict` instead of `key in dict.keys()` | Rule: SIM118 | Columns: 13-47 | Fix: Remove `.keys()`", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 641, "start_line": 641, "start_column": 13}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (141 > 120)", "description": "Line too long (141 > 120) | Rule: E501 | Columns: 121-142", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 643, "start_line": 643, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 5 with a constant variable", "description": "Magic value used in comparison, consider replacing 5 with a constant variable | Rule: PLR2004 | Columns: 30-31", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 647, "start_line": 647, "start_column": 30}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "C901: `_tick` is too complex (11 > 10)", "description": "`_tick` is too complex (11 > 10) | Rule: C901 | Columns: 15-20", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 661, "start_line": 661, "start_column": 15}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 4 with a constant variable", "description": "Magic value used in comparison, consider replacing 4 with a constant variable | Rule: PLR2004 | Columns: 47-48", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 664, "start_line": 664, "start_column": 47}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA102: Missing `from __future__ import annotations`, but uses PEP 604 union", "description": "Missing `from __future__ import annotations`, but uses PEP 604 union | Rule: FA102 | Columns: 63-81 | Fix: Add `from __future__ import annotations`", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 689, "start_line": 689, "start_column": 63}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SIM108: Use ternary operator `included_roles = [Role.MAFIA] if self.game_state.phase == Phase.NIGHT else []` instead of `if`-`else`-block", "description": "Use ternary operator `included_roles = [Role.MAFIA] if self.game_state.phase == Phase.NIGHT else []` instead of `if`-`else`-block | Rule: SIM108 | Columns: 9-32 | Fix: Replace `if`-`else`-block with `included_roles = [Role.MAFIA] if self.game_state.phase == Phase.NIGHT else []`", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 692, "start_line": 692, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 4 with a constant variable", "description": "Magic value used in comparison, consider replacing 4 with a constant variable | Rule: PLR2004 | Columns: 56-57", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 766, "start_line": 766, "start_column": 56}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF006: Store a reference to the return value of `asyncio.create_task`", "description": "Store a reference to the return value of `asyncio.create_task` | Rule: RUF006 | Columns: 21-77", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 768, "start_line": 768, "start_column": 21}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 17-22 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 780, "start_line": 780, "start_column": 17}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (144 > 120)", "description": "Line too long (144 > 120) | Rule: E501 | Columns: 121-145", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 780, "start_line": 780, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 35-97", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_manager.py", "line_number": 834, "start_line": 834, "start_column": 35}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 23-27", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 37, "start_line": 37, "start_column": 23}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Optional`", "description": "Missing `from __future__ import annotations`, but uses `typing.Optional` | Rule: FA100 | Columns: 22-30", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 38, "start_line": 38, "start_column": 22}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Optional`", "description": "Missing `from __future__ import annotations`, but uses `typing.Optional` | Rule: FA100 | Columns: 47-55", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 58, "start_line": 58, "start_column": 47}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 23-73", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 64, "start_line": 64, "start_column": 23}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM101: Exception must not use a string literal, assign to variable first", "description": "Exception must not use a string literal, assign to variable first | Rule: EM101 | Columns: 34-72 | Fix: Assign to variable; remove string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 64, "start_line": 64, "start_column": 34}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Optional`", "description": "Missing `from __future__ import annotations`, but uses `typing.Optional` | Rule: FA100 | Columns: 45-53", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 72, "start_line": 72, "start_column": 45}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Optional`", "description": "Missing `from __future__ import annotations`, but uses `typing.Optional` | Rule: FA100 | Columns: 85-93", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 72, "start_line": 72, "start_column": 85}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 23-76", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 78, "start_line": 78, "start_column": 23}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM102: Exception must not use an f-string literal, assign to variable first", "description": "Exception must not use an f-string literal, assign to variable first | Rule: EM102 | Columns: 34-75 | Fix: Assign to variable; remove f-string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 78, "start_line": 78, "start_column": 34}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET505: Unnecessary `else` after `return` statement", "description": "Unnecessary `else` after `return` statement | Rule: RET505 | Columns: 13-17", "file_path": "/tmp/tmp_50dywyi/backend/app/domain/game_state.py", "line_number": 100, "start_line": 100, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/app/main.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TD002: Missing author in TODO; try: `# TODO(<author_name>): ...` or `# TODO @<author_name>: ...`", "description": "Missing author in TODO; try: `# TODO(<author_name>): ...` or `# TODO @<author_name>: ...` | Rule: TD002 | Columns: 3-7", "file_path": "/tmp/tmp_50dywyi/backend/app/main.py", "line_number": 19, "start_line": 19, "start_column": 3}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TD003: Missing issue link on the line following this TODO", "description": "Missing issue link on the line following this TODO | Rule: TD003 | Columns: 3-7", "file_path": "/tmp/tmp_50dywyi/backend/app/main.py", "line_number": 19, "start_line": 19, "start_column": 3}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FIX002: Line contains TODO, consider resolving the issue", "description": "Line contains TODO, consider resolving the issue | Rule: FIX002 | Columns: 3-7", "file_path": "/tmp/tmp_50dywyi/backend/app/main.py", "line_number": 19, "start_line": 19, "start_column": 3}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F401: `schemas.game.PlayerLeavePayload` imported but unused", "description": "`schemas.game.PlayerLeavePayload` imported but unused | Rule: F401 | Columns: 5-23 | Fix: Remove unused import: `schemas.game.PlayerLeavePayload`", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 21, "start_line": 21, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 30-58", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 40, "start_line": 40, "start_column": 30}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 29-35", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 43, "start_line": 43, "start_column": 29}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 21-25", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 47, "start_line": 47, "start_column": 21}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 16-20", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 50, "start_line": 50, "start_column": 16}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY301: Abstract `raise` to an inner function", "description": "Abstract `raise` to an inner function | Rule: TRY301 | Columns: 25-46", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 84, "start_line": 84, "start_column": 25}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PERF203: `try`-`except` within a loop incurs performance overhead", "description": "`try`-`except` within a loop incurs performance overhead | Rule: PERF203 | Columns: 13-18", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 89, "start_line": 89, "start_column": 13}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "ERA001: Found commented-out code", "description": "Found commented-out code | Rule: ERA001 | Columns: 13-94 | Fix: <PERSON><PERSON>ve commented-out code", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 103, "start_line": 103, "start_column": 13}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "ERA001: Found commented-out code", "description": "Found commented-out code | Rule: ERA001 | Columns: 13-15 | Fix: <PERSON><PERSON>ve commented-out code", "file_path": "/tmp/tmp_50dywyi/backend/app/routers/websocket.py", "line_number": 104, "start_line": 104, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "INP001: File `backend/app/services/character_generator.py` is part of an implicit namespace package. Add an `__init__.py`.", "description": "File `backend/app/services/character_generator.py` is part of an implicit namespace package. Add an `__init__.py`. | Rule: INP001 | Column: 1", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F401: `asyncio` imported but unused", "description": "`asyncio` imported but unused | Rule: F401 | Columns: 8-15 | Fix: Remove unused import: `asyncio`", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 4, "start_line": 4, "start_column": 8}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 5-55 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 11, "start_line": 11, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 5-55 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 11, "start_line": 11, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR0913: Too many arguments in function definition (6 > 5)", "description": "Too many arguments in function definition (6 > 5) | Rule: PLR0913 | Columns: 9-17", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 13, "start_line": 13, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 26-30", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 20, "start_line": 20, "start_column": 26}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-44 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 21, "start_line": 21, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-44 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 21, "start_line": 21, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 5-62 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 31, "start_line": 31, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 5-62 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 31, "start_line": 31, "start_column": 5}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.List`", "description": "Missing `from __future__ import annotations`, but uses `typing.List` | Rule: FA100 | Columns: 58-62", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 42, "start_line": 42, "start_column": 58}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 63-67", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 42, "start_line": 42, "start_column": 63}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.List`", "description": "Missing `from __future__ import annotations`, but uses `typing.List` | Rule: FA100 | Columns: 83-87", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 42, "start_line": 42, "start_column": 83}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 43, "start_line": 43, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 43, "start_line": 43, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 43, "start_line": 43, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 43, "start_line": 43, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 43, "start_line": 43, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 54, "start_line": 54, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 57, "start_line": 57, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 25-91", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 64, "start_line": 64, "start_column": 25}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 21-68", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 71, "start_line": 71, "start_column": 21}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 88, "start_line": 88, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 25-72", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 89, "start_line": 89, "start_column": 25}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W291: Trailing whitespace", "description": "Trailing whitespace | Rule: W291 | Columns: 73-74 | Fix: <PERSON>move trailing whitespace", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 89, "start_line": 89, "start_column": 73}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 25-69", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 96, "start_line": 96, "start_column": 25}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY300: Consider moving this statement to an `else` block", "description": "Consider moving this statement to an `else` block | Rule: TRY300 | Columns: 13-27", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 97, "start_line": 97, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 98, "start_line": 98, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 99, "start_line": 99, "start_column": 16}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 28-78", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 100, "start_line": 100, "start_column": 28}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 70-76 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 100, "start_line": 100, "start_column": 70}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.List`", "description": "Missing `from __future__ import annotations`, but uses `typing.List` | Rule: FA100 | Columns: 57-61", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 103, "start_line": 103, "start_column": 57}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 104, "start_line": 104, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 104, "start_line": 104, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 104, "start_line": 104, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 104, "start_line": 104, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 104, "start_line": 104, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET505: Unnecessary `else` after `return` statement", "description": "Unnecessary `else` after `return` statement | Rule: RET505 | Columns: 9-13", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 115, "start_line": 115, "start_column": 9}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S311: Standard pseudo-random generators are not suitable for cryptographic purposes", "description": "Standard pseudo-random generators are not suitable for cryptographic purposes | Rule: S311 | Columns: 20-78", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 116, "start_line": 116, "start_column": 20}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 119, "start_line": 119, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 119, "start_line": 119, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 119, "start_line": 119, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 119, "start_line": 119, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 119, "start_line": 119, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (184 > 120)", "description": "Line too long (184 > 120) | Rule: E501 | Columns: 121-185", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 145, "start_line": 145, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET504: Unnecessary assignment to `prompt` before `return` statement", "description": "Unnecessary assignment to `prompt` before `return` statement | Rule: RET504 | Columns: 16-22 | Fix: Remove unnecessary assignment", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 147, "start_line": 147, "start_column": 16}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 150, "start_line": 150, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 150, "start_line": 150, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 150, "start_line": 150, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 150, "start_line": 150, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 150, "start_line": 150, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 175, "start_line": 175, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 175, "start_line": 175, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 175, "start_line": 175, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 175, "start_line": 175, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 175, "start_line": 175, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 177, "start_line": 177, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 182, "start_line": 182, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 194, "start_line": 194, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W291: Trailing whitespace", "description": "Trailing whitespace | Rule: W291 | Columns: 24-25 | Fix: Remove trailing whitespace", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 196, "start_line": 196, "start_column": 24}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 199, "start_line": 199, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 201, "start_line": 201, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 211, "start_line": 211, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 211, "start_line": 211, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 211, "start_line": 211, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 211, "start_line": 211, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 211, "start_line": 211, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 213, "start_line": 213, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 216, "start_line": 216, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W291: Trailing whitespace", "description": "Trailing whitespace | Rule: W291 | Columns: 30-31 | Fix: Remove trailing whitespace", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 222, "start_line": 222, "start_column": 30}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W292: No newline at end of file", "description": "No newline at end of file | Rule: W292 | Column: 46 | Fix: Add trailing newline", "file_path": "/tmp/tmp_50dywyi/backend/app/services/character_generator.py", "line_number": 238, "start_line": 238, "start_column": 46}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "INP001: File `backend/app/services/llm_client.py` is part of an implicit namespace package. Add an `__init__.py`.", "description": "File `backend/app/services/llm_client.py` is part of an implicit namespace package. Add an `__init__.py`. | Rule: INP001 | Column: 1", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F401: `json` imported but unused", "description": "`json` imported but unused | Rule: F401 | Columns: 8-12 | Fix: Remove unused import: `json`", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 2, "start_line": 2, "start_column": 8}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F401: `typing.Optional` imported but unused", "description": "`typing.Optional` imported but unused | Rule: F401 | Columns: 20-28 | Fix: Remove unused import: `typing.Optional`", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 3, "start_line": 3, "start_column": 20}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 5-49 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 13, "start_line": 13, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 5-49 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 13, "start_line": 13, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF013: PEP 484 prohibits implicit `Optional`", "description": "PEP 484 prohibits implicit `Optional` | Rule: RUF013 | Columns: 33-36 | Fix: Convert to `Optional[T]`", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 15, "start_line": 15, "start_column": 33}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 19-131", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 18, "start_line": 18, "start_column": 19}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM101: Exception must not use a string literal, assign to variable first", "description": "Exception must not use a string literal, assign to variable first | Rule: EM101 | Columns: 30-130 | Fix: Assign to variable; remove string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 18, "start_line": 18, "start_column": 30}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (130 > 120)", "description": "Line too long (130 > 120) | Rule: E501 | Columns: 121-131", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 18, "start_line": 18, "start_column": 121}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 23, "start_line": 23, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 23, "start_line": 23, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 23, "start_line": 23, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 23, "start_line": 23, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 23, "start_line": 23, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET505: Unnecessary `elif` after `return` statement", "description": "Unnecessary `elif` after `return` statement | Rule: RET505 | Columns: 21-25", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 60, "start_line": 60, "start_column": 21}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY301: Abstract `raise` to an inner function", "description": "Abstract `raise` to an inner function | Rule: TRY301 | Columns: 25-90", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 64, "start_line": 64, "start_column": 25}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 31-90", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 64, "start_line": 64, "start_column": 31}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 31-90", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 64, "start_line": 64, "start_column": 31}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM102: Exception must not use an f-string literal, assign to variable first", "description": "Exception must not use an f-string literal, assign to variable first | Rule: EM102 | Columns: 41-89 | Fix: Assign to variable; remove f-string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 64, "start_line": 64, "start_column": 41}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY301: Abstract `raise` to an inner function", "description": "Abstract `raise` to an inner function | Rule: TRY301 | Columns: 21-71", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 66, "start_line": 66, "start_column": 21}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 27-71", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 66, "start_line": 66, "start_column": 27}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 27-71", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 66, "start_line": 66, "start_column": 27}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM102: Exception must not use an f-string literal, assign to variable first", "description": "Exception must not use an f-string literal, assign to variable first | Rule: EM102 | Columns: 37-70 | Fix: Assign to variable; remove f-string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 66, "start_line": 66, "start_column": 37}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY301: Abstract `raise` to an inner function", "description": "Abstract `raise` to an inner function | Rule: TRY301 | Columns: 17-76", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 68, "start_line": 68, "start_column": 17}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 23-76", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 68, "start_line": 68, "start_column": 23}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 23-76", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 68, "start_line": 68, "start_column": 23}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM102: Exception must not use an f-string literal, assign to variable first", "description": "Exception must not use an f-string literal, assign to variable first | Rule: EM102 | Columns: 33-75 | Fix: Assign to variable; remove f-string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 68, "start_line": 68, "start_column": 33}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 70, "start_line": 70, "start_column": 16}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 13-61", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 71, "start_line": 71, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-60", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 71, "start_line": 71, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 52-58 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 71, "start_line": 71, "start_column": 52}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "B904: Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling", "description": "Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling | Rule: B904 | Columns: 13-66", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 72, "start_line": 72, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY200: Use `raise from` to specify exception cause", "description": "Use `raise from` to specify exception cause | Rule: TRY200 | Columns: 13-66", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 72, "start_line": 72, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 19-66", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 72, "start_line": 72, "start_column": 19}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 19-66", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 72, "start_line": 72, "start_column": 19}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM102: Exception must not use an f-string literal, assign to variable first", "description": "Exception must not use an f-string literal, assign to variable first | Rule: EM102 | Columns: 29-65 | Fix: Assign to variable; remove f-string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 72, "start_line": 72, "start_column": 29}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 57-63 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 72, "start_line": 72, "start_column": 57}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 38-42", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 74, "start_line": 74, "start_column": 38}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 57-61", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 74, "start_line": 74, "start_column": 57}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 75, "start_line": 75, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 77, "start_line": 77, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 80, "start_line": 80, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 86, "start_line": 86, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 200 with a constant variable", "description": "Magic value used in comparison, consider replacing 200 with a constant variable | Rule: PLR2004 | Columns: 40-43", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 90, "start_line": 90, "start_column": 40}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 92, "start_line": 92, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY300: Consider moving this statement to an `else` block", "description": "Consider moving this statement to an `else` block | Rule: TRY300 | Columns: 13-33", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 94, "start_line": 94, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET504: Unnecessary assignment to `response_data` before `return` statement", "description": "Unnecessary assignment to `response_data` before `return` statement | Rule: RET504 | Columns: 20-33 | Fix: Remove unnecessary assignment", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 94, "start_line": 94, "start_column": 20}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 95, "start_line": 95, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "B904: Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling", "description": "Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling | Rule: B904 | Columns: 13-64", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 97, "start_line": 97, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY200: Use `raise from` to specify exception cause", "description": "Use `raise from` to specify exception cause | Rule: TRY200 | Columns: 13-64", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 97, "start_line": 97, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 19-64", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 97, "start_line": 97, "start_column": 19}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 19-64", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 97, "start_line": 97, "start_column": 19}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM101: Exception must not use a string literal, assign to variable first", "description": "Exception must not use a string literal, assign to variable first | Rule: EM101 | Columns: 29-63 | Fix: Assign to variable; remove string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 97, "start_line": 97, "start_column": 29}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "B904: Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling", "description": "Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling | Rule: B904 | Columns: 13-85", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 99, "start_line": 99, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY200: Use `raise from` to specify exception cause", "description": "Use `raise from` to specify exception cause | Rule: TRY200 | Columns: 13-85", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 99, "start_line": 99, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 19-85", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 99, "start_line": 99, "start_column": 19}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 19-85", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 99, "start_line": 99, "start_column": 19}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM102: Exception must not use an f-string literal, assign to variable first", "description": "Exception must not use an f-string literal, assign to variable first | Rule: EM102 | Columns: 29-84 | Fix: Assign to variable; remove f-string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 99, "start_line": 99, "start_column": 29}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 76-82 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 99, "start_line": 99, "start_column": 76}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 35-39", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 101, "start_line": 101, "start_column": 35}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 102, "start_line": 102, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 102, "start_line": 102, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 102, "start_line": 102, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 102, "start_line": 102, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D406: Section name should end with a newline (\"Returns\")", "description": "Section name should end with a newline (\"Returns\") | Rule: D406 | Columns: 9-12 | Fix: Add newline after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 102, "start_line": 102, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 117, "start_line": 117, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 117, "start_line": 117, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Args\")", "description": "Missing blank line after last section (\"Args\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 117, "start_line": 117, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 117, "start_line": 117, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 401 with a constant variable", "description": "Magic value used in comparison, consider replacing 401 with a constant variable | Rule: PLR2004 | Columns: 36-39", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 123, "start_line": 123, "start_column": 36}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 19-85", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 124, "start_line": 124, "start_column": 19}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 19-85", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 124, "start_line": 124, "start_column": 19}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM101: Exception must not use a string literal, assign to variable first", "description": "Exception must not use a string literal, assign to variable first | Rule: EM101 | Columns: 29-84 | Fix: Assign to variable; remove string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 124, "start_line": 124, "start_column": 29}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET506: Unnecessary `elif` after `raise` statement", "description": "Unnecessary `elif` after `raise` statement | Rule: RET506 | Columns: 9-13", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 125, "start_line": 125, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 429 with a constant variable", "description": "Magic value used in comparison, consider replacing 429 with a constant variable | Rule: PLR2004 | Columns: 38-41", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 125, "start_line": 125, "start_column": 38}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 19-71", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 126, "start_line": 126, "start_column": 19}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 19-71", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 126, "start_line": 126, "start_column": 19}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM101: Exception must not use a string literal, assign to variable first", "description": "Exception must not use a string literal, assign to variable first | Rule: EM101 | Columns: 29-70 | Fix: Assign to variable; remove string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 126, "start_line": 126, "start_column": 29}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 500 with a constant variable", "description": "Magic value used in comparison, consider replacing 500 with a constant variable | Rule: PLR2004 | Columns: 38-41", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 127, "start_line": 127, "start_column": 38}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 19-75", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 128, "start_line": 128, "start_column": 19}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 19-75", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 128, "start_line": 128, "start_column": 19}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM101: Exception must not use a string literal, assign to variable first", "description": "Exception must not use a string literal, assign to variable first | Rule: EM101 | Columns: 29-74 | Fix: Assign to variable; remove string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 128, "start_line": 128, "start_column": 29}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 400 with a constant variable", "description": "Magic value used in comparison, consider replacing 400 with a constant variable | Rule: PLR2004 | Columns: 38-41", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 129, "start_line": 129, "start_column": 38}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 23-72", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 133, "start_line": 133, "start_column": 23}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 23-72", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 133, "start_line": 133, "start_column": 23}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM102: Exception must not use an f-string literal, assign to variable first", "description": "Exception must not use an f-string literal, assign to variable first | Rule: EM102 | Columns: 33-71 | Fix: Assign to variable; remove f-string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 133, "start_line": 133, "start_column": 33}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E722: Do not use bare `except`", "description": "Do not use bare `except` | Rule: E722 | Columns: 13-19", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 134, "start_line": 134, "start_column": 13}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "B904: Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling", "description": "Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling | Rule: B904 | Columns: 17-84", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 135, "start_line": 135, "start_column": 17}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY200: Use `raise from` to specify exception cause", "description": "Use `raise from` to specify exception cause | Rule: TRY200 | Columns: 17-84", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 135, "start_line": 135, "start_column": 17}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY002: Create your own exception", "description": "Create your own exception | Rule: TRY002 | Columns: 23-84", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 135, "start_line": 135, "start_column": 23}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY003: Avoid specifying long messages outside the exception class", "description": "Avoid specifying long messages outside the exception class | Rule: TRY003 | Columns: 23-84", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 135, "start_line": 135, "start_column": 23}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "EM102: Exception must not use an f-string literal, assign to variable first", "description": "Exception must not use an f-string literal, assign to variable first | Rule: EM102 | Columns: 33-83 | Fix: Assign to variable; remove f-string literal", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 135, "start_line": 135, "start_column": 33}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W292: No newline at end of file", "description": "No newline at end of file | Rule: W292 | Column: 84 | Fix: Add trailing newline", "file_path": "/tmp/tmp_50dywyi/backend/app/services/llm_client.py", "line_number": 135, "start_line": 135, "start_column": 84}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "INP001: File `backend/app/services/narrator_service.py` is part of an implicit namespace package. Add an `__init__.py`.", "description": "File `backend/app/services/narrator_service.py` is part of an implicit namespace package. Add an `__init__.py`. | Rule: INP001 | Column: 1", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 5-55 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 10, "start_line": 10, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 5-55 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 10, "start_line": 10, "start_column": 5}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 34-38", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 17, "start_line": 17, "start_column": 34}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 51-55", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 19, "start_line": 19, "start_column": 51}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-40 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 20, "start_line": 20, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-40 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 20, "start_line": 20, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 20 with a constant variable", "description": "Magic value used in comparison, consider replacing 20 with a constant variable | Rule: PLR2004 | Columns: 31-33", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 28, "start_line": 28, "start_column": 31}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-37 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 32, "start_line": 32, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-37 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 32, "start_line": 32, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 5-64 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 41, "start_line": 41, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 5-64 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 41, "start_line": 41, "start_column": 5}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.List`", "description": "Missing `from __future__ import annotations`, but uses `typing.List` | Rule: FA100 | Columns: 48-52", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 47, "start_line": 47, "start_column": 48}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 48, "start_line": 48, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 48, "start_line": 48, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Args\")", "description": "Missing blank line after last section (\"Args\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 48, "start_line": 48, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 48, "start_line": 48, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 25-106", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 55, "start_line": 55, "start_column": 25}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 57, "start_line": 57, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 21-84", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 58, "start_line": 58, "start_column": 21}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.List`", "description": "Missing `from __future__ import annotations`, but uses `typing.List` | Rule: FA100 | Columns: 52-56", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 60, "start_line": 60, "start_column": 52}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 61, "start_line": 61, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 61, "start_line": 61, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 61, "start_line": 61, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 61, "start_line": 61, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 61, "start_line": 61, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 63, "start_line": 63, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 66, "start_line": 66, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 78, "start_line": 78, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 80, "start_line": 80, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 102, "start_line": 102, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 107, "start_line": 107, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 109, "start_line": 109, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 110, "start_line": 110, "start_column": 16}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 13-72", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 111, "start_line": 111, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-71", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 111, "start_line": 111, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 63-69 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 111, "start_line": 111, "start_column": 63}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (199 > 120)", "description": "Line too long (199 > 120) | Rule: E501 | Columns: 121-200", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 112, "start_line": 112, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "ARG002: Unused method argument: `context`", "description": "Unused method argument: `context` | Rule: ARG002 | Columns: 76-83", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 114, "start_line": 114, "start_column": 76}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 85-89", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 114, "start_line": 114, "start_column": 85}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 115, "start_line": 115, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 115, "start_line": 115, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 115, "start_line": 115, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 115, "start_line": 115, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 115, "start_line": 115, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 117, "start_line": 117, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 122, "start_line": 122, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 128, "start_line": 128, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 133, "start_line": 133, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 140, "start_line": 140, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 163, "start_line": 163, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 169, "start_line": 169, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 171, "start_line": 171, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 172, "start_line": 172, "start_column": 16}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 13-74", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 173, "start_line": 173, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-73", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 173, "start_line": 173, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 65-71 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 173, "start_line": 173, "start_column": 65}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (228 > 120)", "description": "Line too long (228 > 120) | Rule: E501 | Columns: 121-229", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 175, "start_line": 175, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET505: Unnecessary `else` after `return` statement", "description": "Unnecessary `else` after `return` statement | Rule: RET505 | Columns: 13-17", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 176, "start_line": 176, "start_column": 13}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (210 > 120)", "description": "Line too long (210 > 120) | Rule: E501 | Columns: 121-211", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 177, "start_line": 177, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 74-78", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 179, "start_line": 179, "start_column": 74}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 180, "start_line": 180, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 180, "start_line": 180, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 180, "start_line": 180, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 180, "start_line": 180, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 180, "start_line": 180, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 182, "start_line": 182, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 186, "start_line": 186, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 192, "start_line": 192, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 197, "start_line": 197, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 200, "start_line": 200, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 224, "start_line": 224, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 230, "start_line": 230, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 232, "start_line": 232, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 233, "start_line": 233, "start_column": 16}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 13-75", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 234, "start_line": 234, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-74", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 234, "start_line": 234, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 66-72 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 234, "start_line": 234, "start_column": 66}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (125 > 120)", "description": "Line too long (125 > 120) | Rule: E501 | Columns: 121-126", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 235, "start_line": 235, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "ARG002: Unused method argument: `context`", "description": "Unused method argument: `context` | Rule: ARG002 | Columns: 58-65", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 237, "start_line": 237, "start_column": 58}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 67-71", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 237, "start_line": 237, "start_column": 67}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 238, "start_line": 238, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 238, "start_line": 238, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 238, "start_line": 238, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 238, "start_line": 238, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 238, "start_line": 238, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 285, "start_line": 285, "start_column": 16}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 13-73", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 286, "start_line": 286, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-72", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 286, "start_line": 286, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 64-70 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 286, "start_line": 286, "start_column": 64}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 290, "start_line": 290, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 290, "start_line": 290, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 290, "start_line": 290, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 290, "start_line": 290, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 290, "start_line": 290, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 292, "start_line": 292, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 296, "start_line": 296, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 307, "start_line": 307, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 309, "start_line": 309, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 333, "start_line": 333, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-17 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 336, "start_line": 336, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 342, "start_line": 342, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 344, "start_line": 344, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 345, "start_line": 345, "start_column": 16}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 13-75", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 346, "start_line": 346, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-74", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 346, "start_line": 346, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 66-72 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 346, "start_line": 346, "start_column": 66}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (194 > 120)", "description": "Line too long (194 > 120) | Rule: E501 | Columns: 121-195", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 348, "start_line": 348, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (181 > 120)", "description": "Line too long (181 > 120) | Rule: E501 | Columns: 121-182", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 349, "start_line": 349, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (179 > 120)", "description": "Line too long (179 > 120) | Rule: E501 | Columns: 121-180", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 350, "start_line": 350, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (178 > 120)", "description": "Line too long (178 > 120) | Rule: E501 | Columns: 121-179", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 351, "start_line": 351, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.List`", "description": "Missing `from __future__ import annotations`, but uses `typing.List` | Rule: FA100 | Columns: 64-68", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 355, "start_line": 355, "start_column": 64}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 356, "start_line": 356, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 356, "start_line": 356, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 356, "start_line": 356, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 356, "start_line": 356, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 356, "start_line": 356, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 358, "start_line": 358, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 362, "start_line": 362, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 374, "start_line": 374, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 376, "start_line": 376, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 386, "start_line": 386, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 408, "start_line": 408, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 414, "start_line": 414, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-13 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 416, "start_line": 416, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 16-25", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 417, "start_line": 417, "start_column": 16}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TRY400: Use `logging.exception` instead of `logging.error`", "description": "Use `logging.exception` instead of `logging.error` | Rule: TRY400 | Columns: 13-70", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 418, "start_line": 418, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "G004: Logging statement uses f-string", "description": "Logging statement uses f-string | Rule: G004 | Columns: 26-69", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 418, "start_line": 418, "start_column": 26}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 61-67 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 418, "start_line": 418, "start_column": 61}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RET505: Unnecessary `elif` after `return` statement", "description": "Unnecessary `elif` after `return` statement | Rule: RET505 | Columns: 13-17", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 421, "start_line": 421, "start_column": 13}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (123 > 120)", "description": "Line too long (123 > 120) | Rule: E501 | Columns: 121-124", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 422, "start_line": 422, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 67-71", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 426, "start_line": 426, "start_column": 67}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 427, "start_line": 427, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 427, "start_line": 427, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 427, "start_line": 427, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 427, "start_line": 427, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 427, "start_line": 427, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TD002: Missing author in TODO; try: `# TODO(<author_name>): ...` or `# TODO @<author_name>: ...`", "description": "Missing author in TODO; try: `# TODO(<author_name>): ...` or `# TODO @<author_name>: ...` | Rule: TD002 | Columns: 11-15", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 437, "start_line": 437, "start_column": 11}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "TD003: Missing issue link on the line following this TODO", "description": "Missing issue link on the line following this TODO | Rule: TD003 | Columns: 11-15", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 437, "start_line": 437, "start_column": 11}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FIX002: Line contains TODO, consider resolving the issue", "description": "Line contains TODO, consider resolving the issue | Rule: FIX002 | Columns: 11-15", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 437, "start_line": 437, "start_column": 11}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PIE790: Unnecessary `pass` statement", "description": "Unnecessary `pass` statement | Rule: PIE790 | Columns: 9-13 | Fix: Remove unnecessary `pass`", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 438, "start_line": 438, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Optional`", "description": "Missing `from __future__ import annotations`, but uses `typing.Optional` | Rule: FA100 | Columns: 59-67", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 440, "start_line": 440, "start_column": 59}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 441, "start_line": 441, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 441, "start_line": 441, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 441, "start_line": 441, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Returns\")", "description": "Missing blank line after last section (\"Returns\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 441, "start_line": 441, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Returns\")", "description": "Missing dashed underline after section (\"Returns\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Returns\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 441, "start_line": 441, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 62-66", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 452, "start_line": 452, "start_column": 62}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 9-12 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 453, "start_line": 453, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 9-12 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 453, "start_line": 453, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D413: Missing blank line after last section (\"Args\")", "description": "Missing blank line after last section (\"Args\") | Rule: D413 | Columns: 9-12 | Fix: Add blank line after \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 453, "start_line": 453, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D407: Missing dashed underline after section (\"Args\")", "description": "Missing dashed underline after section (\"Args\") | Rule: D407 | Columns: 9-12 | Fix: Add dashed line under \"Args\"", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 453, "start_line": 453, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PLR2004: Magic value used in comparison, consider replacing 2 with a constant variable", "description": "Magic value used in comparison, consider replacing 2 with a constant variable | Rule: PLR2004 | Columns: 46-47", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 469, "start_line": 469, "start_column": 46}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W292: No newline at end of file", "description": "No newline at end of file | Rule: W292 | Column: 61 | Fix: Add trailing newline", "file_path": "/tmp/tmp_50dywyi/backend/app/services/narrator_service.py", "line_number": 472, "start_line": 472, "start_column": 61}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Optional`", "description": "Missing `from __future__ import annotations`, but uses `typing.Optional` | Rule: FA100 | Columns: 14-22", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 71, "start_line": 71, "start_column": 14}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Optional`", "description": "Missing `from __future__ import annotations`, but uses `typing.Optional` | Rule: FA100 | Columns: 20-28", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 166, "start_line": 166, "start_column": 20}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 14-18", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 174, "start_line": 174, "start_column": 14}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.List`", "description": "Missing `from __future__ import annotations`, but uses `typing.List` | Rule: FA100 | Columns: 14-18", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 178, "start_line": 178, "start_column": 14}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FBT003: Boolean positional value in function call", "description": "Boolean positional value in function call | Rule: FBT003 | Columns: 35-40", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 183, "start_line": 183, "start_column": 35}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Optional`", "description": "Missing `from __future__ import annotations`, but uses `typing.Optional` | Rule: FA100 | Columns: 12-20", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 184, "start_line": 184, "start_column": 12}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Dict`", "description": "Missing `from __future__ import annotations`, but uses `typing.Dict` | Rule: FA100 | Columns: 21-25", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 184, "start_line": 184, "start_column": 21}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.Optional`", "description": "Missing `from __future__ import annotations`, but uses `typing.Optional` | Rule: FA100 | Columns: 13-21", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 187, "start_line": 187, "start_column": 13}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "FA100: Missing `from __future__ import annotations`, but uses `typing.List`", "description": "Missing `from __future__ import annotations`, but uses `typing.List` | Rule: FA100 | Columns: 11-15", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 190, "start_line": 190, "start_column": 11}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W292: No newline at end of file", "description": "No newline at end of file | Rule: W292 | Column: 37 | Fix: Add trailing newline", "file_path": "/tmp/tmp_50dywyi/backend/schemas/game.py", "line_number": 234, "start_line": 234, "start_column": 37}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "INP001: File `backend/tests/test_character_generator.py` is part of an implicit namespace package. Add an `__init__.py`.", "description": "File `backend/tests/test_character_generator.py` is part of an implicit namespace package. Add an `__init__.py`. | Rule: INP001 | Column: 1", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E402: Module level import not at top of file", "description": "Module level import not at top of file | Rule: E402 | Columns: 1-51", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 8, "start_line": 8, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 8, "start_line": 8, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E402: Module level import not at top of file", "description": "Module level import not at top of file | Rule: E402 | Columns: 1-64", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 9, "start_line": 9, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D202: No blank lines allowed after function docstring (found 1)", "description": "No blank lines allowed after function docstring (found 1) | Rule: D202 | Columns: 5-43 | Fix: Remove blank line(s) after function docstring", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 12, "start_line": 12, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 5-43 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 12, "start_line": 12, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 5-43 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 12, "start_line": 12, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-5 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 13, "start_line": 13, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 5-10 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 14, "start_line": 14, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-5 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 15, "start_line": 15, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 20, "start_line": 20, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 21, "start_line": 21, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 28, "start_line": 28, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 29, "start_line": 29, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 30, "start_line": 30, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 33, "start_line": 33, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 34, "start_line": 34, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 35, "start_line": 35, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 13-18 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 38, "start_line": 38, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 13-18 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 39, "start_line": 39, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 13-18 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 40, "start_line": 40, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 13-18 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 41, "start_line": 41, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 42, "start_line": 42, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 44, "start_line": 44, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-58 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 44, "start_line": 44, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 46, "start_line": 46, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 47, "start_line": 47, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 49, "start_line": 49, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-47 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 49, "start_line": 49, "start_column": 15}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SLF001: Private member accessed: `_select_professions`", "description": "Private member accessed: `_select_professions` | Rule: SLF001 | Columns: 23-52", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 50, "start_line": 50, "start_column": 23}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 51, "start_line": 51, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 52, "start_line": 52, "start_column": 1}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SLF001: Private member accessed: `_get_profession_emoji`", "description": "Private member accessed: `_get_profession_emoji` | Rule: SLF001 | Columns: 17-48", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 53, "start_line": 53, "start_column": 17}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 54, "start_line": 54, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 55, "start_line": 55, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 56, "start_line": 56, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-58 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 56, "start_line": 56, "start_column": 15}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 57, "start_line": 57, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 12-21", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 58, "start_line": 58, "start_column": 12}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 59, "start_line": 59, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 27-33 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 59, "start_line": 59, "start_column": 27}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W292: No newline at end of file", "description": "No newline at end of file | Rule: W292 | Column: 31 | Fix: Add trailing newline", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_character_generator.py", "line_number": 64, "start_line": 64, "start_column": 31}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "INP001: File `backend/tests/test_complete_system.py` is part of an implicit namespace package. Add an `__init__.py`.", "description": "File `backend/tests/test_complete_system.py` is part of an implicit namespace package. Add an `__init__.py`. | Rule: INP001 | Column: 1", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E402: Module level import not at top of file", "description": "Module level import not at top of file | Rule: E402 | Columns: 1-51", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 8, "start_line": 8, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 8, "start_line": 8, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E402: Module level import not at top of file", "description": "Module level import not at top of file | Rule: E402 | Columns: 1-64", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 9, "start_line": 9, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E402: Module level import not at top of file", "description": "Module level import not at top of file | Rule: E402 | Columns: 1-58", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 10, "start_line": 10, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D202: No blank lines allowed after function docstring (found 1)", "description": "No blank lines allowed after function docstring (found 1) | Rule: D202 | Columns: 5-75 | Fix: Remove blank line(s) after function docstring", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 13, "start_line": 13, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 5-75 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 13, "start_line": 13, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 5-75 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 13, "start_line": 13, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-5 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 14, "start_line": 14, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 5-10 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 15, "start_line": 15, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-5 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 16, "start_line": 16, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 22, "start_line": 22, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 23, "start_line": 23, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 31, "start_line": 31, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 32, "start_line": 32, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 33, "start_line": 33, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 35, "start_line": 35, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-54 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 35, "start_line": 35, "start_column": 15}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 37, "start_line": 37, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 38, "start_line": 38, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-39 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 38, "start_line": 38, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 13-18 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 40, "start_line": 40, "start_column": 13}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 13-18 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 41, "start_line": 41, "start_column": 13}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 42, "start_line": 42, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 45, "start_line": 45, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 47, "start_line": 47, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-45 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 47, "start_line": 47, "start_column": 15}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 48, "start_line": 48, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 52, "start_line": 52, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-33 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 52, "start_line": 52, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 53, "start_line": 53, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 54, "start_line": 54, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 57, "start_line": 57, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-27 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 57, "start_line": 57, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 58, "start_line": 58, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 59, "start_line": 59, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 62, "start_line": 62, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-36 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 62, "start_line": 62, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 63, "start_line": 63, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 64, "start_line": 64, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 67, "start_line": 67, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-35 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 67, "start_line": 67, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 68, "start_line": 68, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 69, "start_line": 69, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 72, "start_line": 72, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-37 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 72, "start_line": 72, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 73, "start_line": 73, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 74, "start_line": 74, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 77, "start_line": 77, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-31 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 77, "start_line": 77, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 78, "start_line": 78, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 79, "start_line": 79, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 80, "start_line": 80, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-54 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 80, "start_line": 80, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 81, "start_line": 81, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 82, "start_line": 82, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 12-21", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 83, "start_line": 83, "start_column": 12}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 84, "start_line": 84, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 27-33 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 84, "start_line": 84, "start_column": 27}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W292: No newline at end of file", "description": "No newline at end of file | Rule: W292 | Column: 27 | Fix: Add trailing newline", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_complete_system.py", "line_number": 89, "start_line": 89, "start_column": 27}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "INP001: File `backend/tests/test_llm_client.py` is part of an implicit namespace package. Add an `__init__.py`.", "description": "File `backend/tests/test_llm_client.py` is part of an implicit namespace package. Add an `__init__.py`. | Rule: INP001 | Column: 1", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E402: Module level import not at top of file", "description": "Module level import not at top of file | Rule: E402 | Columns: 1-51", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 9, "start_line": 9, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 9, "start_line": 9, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D202: No blank lines allowed after function docstring (found 1)", "description": "No blank lines allowed after function docstring (found 1) | Rule: D202 | Columns: 5-42 | Fix: Remove blank line(s) after function docstring", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 12, "start_line": 12, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 5-42 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 12, "start_line": 12, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 5-42 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 12, "start_line": 12, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-5 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 13, "start_line": 13, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 5-10 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 14, "start_line": 14, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-5 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 15, "start_line": 15, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 19, "start_line": 19, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 20, "start_line": 20, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 23, "start_line": 23, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 24, "start_line": 24, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 26, "start_line": 26, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 27, "start_line": 27, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 30, "start_line": 30, "start_column": 9}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 36, "start_line": 36, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 37, "start_line": 37, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 38, "start_line": 38, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 39, "start_line": 39, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 41, "start_line": 41, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 41-47 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 41, "start_line": 41, "start_column": 41}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 42, "start_line": 42, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 12-21", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 43, "start_line": 43, "start_column": 12}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 44, "start_line": 44, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 27-33 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 44, "start_line": 44, "start_column": 27}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W292: No newline at end of file", "description": "No newline at end of file | Rule: W292 | Column: 27 | Fix: Add trailing newline", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_llm_client.py", "line_number": 47, "start_line": 47, "start_column": 27}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "INP001: File `backend/tests/test_narrator_service.py` is part of an implicit namespace package. Add an `__init__.py`.", "description": "File `backend/tests/test_narrator_service.py` is part of an implicit namespace package. Add an `__init__.py`. | Rule: INP001 | Column: 1", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E402: Module level import not at top of file", "description": "Module level import not at top of file | Rule: E402 | Columns: 1-51", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 8, "start_line": 8, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 8, "start_line": 8, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E402: Module level import not at top of file", "description": "Module level import not at top of file | Rule: E402 | Columns: 1-82", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 9, "start_line": 9, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F401: `app.services.character_generator.CharacterGenerator` imported but unused", "description": "`app.services.character_generator.CharacterGenerator` imported but unused | Rule: F401 | Columns: 46-64 | Fix: Remove unused import: `app.services.character_generator.CharacterGenerator`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 9, "start_line": 9, "start_column": 46}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E402: Module level import not at top of file", "description": "Module level import not at top of file | Rule: E402 | Columns: 1-58", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 10, "start_line": 10, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D202: No blank lines allowed after function docstring (found 1)", "description": "No blank lines allowed after function docstring (found 1) | Rule: D202 | Columns: 5-39 | Fix: Remove blank line(s) after function docstring", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 13, "start_line": 13, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D400: First line should end with a period", "description": "First line should end with a period | Rule: D400 | Columns: 5-39 | Fix: Add period", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 13, "start_line": 13, "start_column": 5}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "D415: First line should end with a period, question mark, or exclamation point", "description": "First line should end with a period, question mark, or exclamation point | Rule: D415 | Columns: 5-39 | Fix: Add closing punctuation", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 13, "start_line": 13, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-5 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 14, "start_line": 14, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 5-10 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 15, "start_line": 15, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-5 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 16, "start_line": 16, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 21, "start_line": 21, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 22, "start_line": 22, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (132 > 120)", "description": "Line too long (132 > 120) | Rule: E501 | Columns: 121-132", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 25, "start_line": 25, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (132 > 120)", "description": "Line too long (132 > 120) | Rule: E501 | Columns: 121-132", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 26, "start_line": 26, "start_column": 121}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (142 > 120)", "description": "Line too long (142 > 120) | Rule: E501 | Columns: 121-142", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 27, "start_line": 27, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 29, "start_line": 29, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 32, "start_line": 32, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 33, "start_line": 33, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 35, "start_line": 35, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-46 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 35, "start_line": 35, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 38, "start_line": 38, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-34 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 38, "start_line": 38, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 39, "start_line": 39, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 40, "start_line": 40, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 42, "start_line": 42, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-49 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 42, "start_line": 42, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 44, "start_line": 44, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-36 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 44, "start_line": 44, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 45, "start_line": 45, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 46, "start_line": 46, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 48, "start_line": 48, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-50 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 48, "start_line": 48, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 51, "start_line": 51, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-37 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 51, "start_line": 51, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 52, "start_line": 52, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 53, "start_line": 53, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 55, "start_line": 55, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-49 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 55, "start_line": 55, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 57, "start_line": 57, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-37 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 57, "start_line": 57, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 58, "start_line": 58, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 59, "start_line": 59, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 61, "start_line": 61, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-44 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 61, "start_line": 61, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 63, "start_line": 63, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-32 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 63, "start_line": 63, "start_column": 15}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 64, "start_line": 64, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 65, "start_line": 65, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 67, "start_line": 67, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-48 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 67, "start_line": 67, "start_column": 15}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SLF001: Private member accessed: `_get_character_context`", "description": "Private member accessed: `_get_character_context` | Rule: SLF001 | Columns: 19-50", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 68, "start_line": 68, "start_column": 19}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 69, "start_line": 69, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 70, "start_line": 70, "start_column": 1}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 71, "start_line": 71, "start_column": 9}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "F541: f-string without any placeholders", "description": "f-string without any placeholders | Rule: F541 | Columns: 15-55 | Fix: Remove extraneous `f` prefix", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 71, "start_line": 71, "start_column": 15}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W293: Blank line contains whitespace", "description": "Blank line contains whitespace | Rule: W293 | Columns: 1-9 | Fix: Remove whitespace from blank line", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 72, "start_line": 72, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "BLE001: Do not catch blind exception: `Exception`", "description": "Do not catch blind exception: `Exception` | Rule: BLE001 | Columns: 12-21", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 73, "start_line": 73, "start_column": 12}, {"category": "Quality", "severity": "Low", "tool": "ruff", "title": "T201: `print` found", "description": "`print` found | Rule: T201 | Columns: 9-14 | Fix: Remove `print`", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 74, "start_line": 74, "start_column": 9}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "RUF010: Use explicit conversion flag", "description": "Use explicit conversion flag | Rule: RUF010 | Columns: 27-33 | Fix: Replace with conversion flag", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 74, "start_line": 74, "start_column": 27}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W292: No newline at end of file", "description": "No newline at end of file | Rule: W292 | Column: 28 | Fix: Add trailing newline", "file_path": "/tmp/tmp_50dywyi/backend/tests/test_narrator_service.py", "line_number": 79, "start_line": 79, "start_column": 28}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 1, "start_line": 1, "start_column": 1}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E501: Line too long (152 > 120)", "description": "Line too long (152 > 120) | Rule: E501 | Columns: 121-153", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 5, "start_line": 5, "start_column": 121}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT001: Use `@pytest.fixture()` over `@pytest.fixture`", "description": "Use `@pytest.fixture()` over `@pytest.fixture` | Rule: PT001 | Columns: 1-16 | Fix: Add parentheses", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 16, "start_line": 16, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT001: Use `@pytest.fixture()` over `@pytest.fixture`", "description": "Use `@pytest.fixture()` over `@pytest.fixture` | Rule: PT001 | Columns: 1-16 | Fix: Add parentheses", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 20, "start_line": 20, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT001: Use `@pytest.fixture()` over `@pytest.fixture`", "description": "Use `@pytest.fixture()` over `@pytest.fixture` | Rule: PT001 | Columns: 1-16 | Fix: Add parentheses", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 24, "start_line": 24, "start_column": 1}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 36, "start_line": 36, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 37, "start_line": 37, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 39, "start_line": 39, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 40, "start_line": 40, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 45, "start_line": 45, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SLF001: Private member accessed: `_get_vote_winner`", "description": "Private member accessed: `_get_vote_winner` | Rule: SLF001 | Columns: 12-31", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 45, "start_line": 45, "start_column": 12}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 50, "start_line": 50, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SLF001: Private member accessed: `_get_vote_winner`", "description": "Private member accessed: `_get_vote_winner` | Rule: SLF001 | Columns: 12-31", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 50, "start_line": 50, "start_column": 12}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SLF001: Private member accessed: `_assign_roles_randomly`", "description": "Private member accessed: `_assign_roles_randomly` | Rule: SLF001 | Columns: 5-30", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 58, "start_line": 58, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 60, "start_line": 60, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 61, "start_line": 61, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 69, "start_line": 69, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 71, "start_line": 71, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 79, "start_line": 79, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 82, "start_line": 82, "start_column": 5}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "ERA001: Found commented-out code", "description": "Found commented-out code | Rule: ERA001 | Columns: 5-21 | Fix: <PERSON>move commented-out code", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 87, "start_line": 87, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 96, "start_line": 96, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT018: Assertion should be broken down into multiple parts", "description": "Assertion should be broken down into multiple parts | Rule: PT018 | Columns: 5-57 | Fix: Break down assertion into multiple parts", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 96, "start_line": 96, "start_column": 5}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "ERA001: Found commented-out code", "description": "Found commented-out code | Rule: ERA001 | Columns: 5-19 | Fix: <PERSON>move commented-out code", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 98, "start_line": 98, "start_column": 5}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E702: Multiple statements on one line (semicolon)", "description": "Multiple statements on one line (semicolon) | Rule: E702 | Columns: 22-23", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 99, "start_line": 99, "start_column": 22}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 110, "start_line": 110, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT018: Assertion should be broken down into multiple parts", "description": "Assertion should be broken down into multiple parts | Rule: PT018 | Columns: 5-57 | Fix: Break down assertion into multiple parts", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 110, "start_line": 110, "start_column": 5}, {"category": "Quality", "severity": "High", "tool": "ruff", "title": "E702: Multiple statements on one line (semicolon)", "description": "Multiple statements on one line (semicolon) | Rule: E702 | Columns: 22-23", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 113, "start_line": 113, "start_column": 22}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 119, "start_line": 119, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 120, "start_line": 120, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "SLF001: Private member accessed: `_assign_roles_randomly`", "description": "Private member accessed: `_assign_roles_randomly` | Rule: SLF001 | Columns: 5-30", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 126, "start_line": 126, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 128, "start_line": 128, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 129, "start_line": 129, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 130, "start_line": 130, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "W292: No newline at end of file", "description": "No newline at end of file | Rule: W292 | Column: 38 | Fix: Add trailing newline", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_manager.py", "line_number": 130, "start_line": 130, "start_column": 38}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT001: Use `@pytest.fixture()` over `@pytest.fixture`", "description": "Use `@pytest.fixture()` over `@pytest.fixture` | Rule: PT001 | Columns: 1-16 | Fix: Add parentheses", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 10, "start_line": 10, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT001: Use `@pytest.fixture()` over `@pytest.fixture`", "description": "Use `@pytest.fixture()` over `@pytest.fixture` | Rule: PT001 | Columns: 1-16 | Fix: Add parentheses", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 16, "start_line": 16, "start_column": 1}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT001: Use `@pytest.fixture()` over `@pytest.fixture`", "description": "Use `@pytest.fixture()` over `@pytest.fixture` | Rule: PT001 | Columns: 1-16 | Fix: Add parentheses", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 22, "start_line": 22, "start_column": 1}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 30, "start_line": 30, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 32, "start_line": 32, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 36, "start_line": 36, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 46, "start_line": 46, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT011: `pytest.raises(ValueError)` is too broad, set the `match` parameter or use a more specific exception", "description": "`pytest.raises(ValueError)` is too broad, set the `match` parameter or use a more specific exception | Rule: PT011 | Columns: 24-34", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 55, "start_line": 55, "start_column": 24}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 65, "start_line": 65, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 66, "start_line": 66, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 72, "start_line": 72, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 73, "start_line": 73, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "PT011: `pytest.raises(ValueError)` is too broad, set the `match` parameter or use a more specific exception", "description": "`pytest.raises(ValueError)` is too broad, set the `match` parameter or use a more specific exception | Rule: PT011 | Columns: 24-34", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 82, "start_line": 82, "start_column": 24}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 92, "start_line": 92, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 93, "start_line": 93, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 100, "start_line": 100, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 101, "start_line": 101, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 107, "start_line": 107, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 108, "start_line": 108, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 115, "start_line": 115, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 116, "start_line": 116, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 121, "start_line": 121, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 122, "start_line": 122, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 130, "start_line": 130, "start_column": 5}, {"category": "Quality", "severity": "Critical", "tool": "ruff", "title": "S101: Use of `assert` detected", "description": "Use of `assert` detected | Rule: S101 | Columns: 5-11", "file_path": "/tmp/tmp_50dywyi/backend/tests/unit/domain/test_game_state.py", "line_number": 131, "start_line": 131, "start_column": 5}, {"category": "Quality", "severity": "Medium", "tool": "ruff", "title": "I001: Import block is un-sorted or un-formatted", "description": "Import block is un-sorted or un-formatted | Rule: I001 | Column: 1 | Fix: Organize imports", "file_path": "/tmp/tmp_50dywyi/setup.py", "line_number": 2, "start_line": 2, "start_column": 1}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B311: Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "description": "Standard pseudo-random generators are not suitable for security/cryptographic purposes. | Test: blacklist | Confidence: HIGH, Severity: LOW | Columns: 19-77 | CWE-330: https://cwe.mitre.org/data/definitions/330.html | Code: 115 else: 116 return random.choices(self.available_professions, k=player_count) 117 | More info: https://bandit.readthedocs.io/en/1.7.5/blacklists/blacklist_calls.html#b311-random", "file_path": "./backend/app/services/character_generator.py", "line_number": 116, "start_line": 116, "start_column": 19, "end_column": 77}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-29 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 35 gm.add_player('p1', '<PERSON>', dummy_player) 36 assert 'p1' in gm.players 37 assert gm.game_state.players['p1']['alive'] | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 36, "start_line": 36, "start_column": 4, "end_column": 29}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-47 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 36 assert 'p1' in gm.players 37 assert gm.game_state.players['p1']['alive'] 38 gm.remove_player('p1') | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 37, "start_line": 37, "start_column": 4, "end_column": 47}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-33 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 38 gm.remove_player('p1') 39 assert 'p1' not in gm.players 40 assert 'p1' not in gm.game_state.players | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 39, "start_line": 39, "start_column": 4, "end_column": 33}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-44 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 39 assert 'p1' not in gm.players 40 assert 'p1' not in gm.game_state.players 41 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 40, "start_line": 40, "start_column": 4, "end_column": 44}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-39 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 44 gm.cast_votes = {'a': 'x', 'b': 'x', 'c': 'y'} 45 assert gm._get_vote_winner() == 'x' 46 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 45, "start_line": 45, "start_column": 4, "end_column": 39}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-40 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 49 gm.cast_votes = {'a': 'x', 'b': 'y'} 50 assert gm._get_vote_winner() is None 51 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 50, "start_line": 50, "start_column": 4, "end_column": 40}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-48 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 59 roles = [gm.game_state.players[uid]['role'] for uid in ['u1', 'u2', 'u3', 'u4']] 60 assert roles[:2] == [Role.MAFIA, Role.MAFIA] 61 ... | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 60, "start_line": 60, "start_column": 4, "end_column": 48}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-54 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 60 assert roles[:2] == [Role.MAFIA, Role.MAFIA] 61 assert roles[2:] == [Role.INNOCENT, Role.INNOCENT] 62 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 61, "start_line": 61, "start_column": 4, "end_column": 54}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-38 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 68 gm.receive_event(vote_event, p1) 69 assert gm.cast_votes['p1'] == 'p2' 70 acks = [e for e in p1.events if getattr(e, 'type', None) =... | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 69, "start_line": 69, "start_column": 4, "end_column": 38}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-39 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 70 acks = [e for e in p1.events if getattr(e, 'type', None) == 'action.ack'] 71 assert acks, \"No ack sent to voter\" 72 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 71, "start_line": 71, "start_column": 4, "end_column": 39}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-29 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 78 gm.receive_event(join, p) 79 assert 'p1' in gm.players 80 leave = PlayerLeave(type='player.leave', payload=PlayerLeavePayload(player... | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 79, "start_line": 79, "start_column": 4, "end_column": 29}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-33 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 81 gm.receive_event(leave, p) 82 assert 'p1' not in gm.players 83 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 82, "start_line": 82, "start_column": 4, "end_column": 33}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-56 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 95 received = [e for e in p2.events if isinstance(e, MessageReceived)] 96 assert received and received[0].payload.text == 'hi' 97 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 96, "start_line": 96, "start_column": 4, "end_column": 56}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-56 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 109 received = [e for e in p2.events if isinstance(e, MessageReceived)] 110 assert received and received[0].payload.text == 'hi' 111 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 110, "start_line": 110, "start_column": 4, "end_column": 56}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-14 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 118 119 assert ack 120 assert not any(isinstance(e, MessageReceived) for e in p2.events) | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 119, "start_line": 119, "start_column": 4, "end_column": 14}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-69 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 119 assert ack 120 assert not any(isinstance(e, MessageReceived) for e in p2.events) 121 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 120, "start_line": 120, "start_column": 4, "end_column": 69}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-48 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 127 roles = [gm.game_state.players[uid]['role'] for uid in ['u1', 'u2', 'u3', 'u4', 'u5']] 128 assert roles[:2] == [Role.MAFIA, Role.MAFIA]... | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 128, "start_line": 128, "start_column": 4, "end_column": 48}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-34 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 128 assert roles[:2] == [Role.MAFIA, Role.MAFIA] 129 assert Role.MEDIC in roles[2:] 130 assert Role.INNOCENT in roles[2:] | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 129, "start_line": 129, "start_column": 4, "end_column": 34}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-37 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 129 assert Role.MEDIC in roles[2:] 130 assert Role.INNOCENT in roles[2:] | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_manager.py", "line_number": 130, "start_line": 130, "start_column": 4, "end_column": 37}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-80 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 29 game_state.add_player(\"p1\", {\"role\": Role.INNOCENT, \"alive\": True}) 30 assert game_state.players == {\"p1\": {\"role\": Role.INNOCENT, \"ali... | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 30, "start_line": 30, "start_column": 4, "end_column": 80}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-35 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 31 game_state.remove_player(\"p1\") 32 assert game_state.players == {} 33 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 32, "start_line": 32, "start_column": 4, "end_column": 35}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-42 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 35 def test_initial_phase_is_night(game_state): 36 assert game_state.phase == Phase.NIGHT 37 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 36, "start_line": 36, "start_column": 4, "end_column": 42}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-42 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 45 day_state.end_day() 46 assert day_state.phase == Phase.VOTING 47 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 46, "start_line": 46, "start_column": 4, "end_column": 42}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-49 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 64 night_state.end_night(\"p1\") 65 assert not night_state.players[\"p1\"][\"alive\"] 66 assert night_state.phase == Phase.DAY | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 65, "start_line": 65, "start_column": 4, "end_column": 49}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-41 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 65 assert not night_state.players[\"p1\"][\"alive\"] 66 assert night_state.phase == Phase.DAY 67 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 66, "start_line": 66, "start_column": 4, "end_column": 41}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-49 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 71 night_state.end_night(\"p1\") 72 assert not night_state.players[\"p1\"][\"alive\"] 73 assert night_state.phase == Phase.ENDED | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 72, "start_line": 72, "start_column": 4, "end_column": 49}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-43 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 72 assert not night_state.players[\"p1\"][\"alive\"] 73 assert night_state.phase == Phase.ENDED 74 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 73, "start_line": 73, "start_column": 4, "end_column": 43}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-50 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 91 voting_state.end_voting(\"p2\") 92 assert not voting_state.players[\"p2\"][\"alive\"] 93 assert voting_state.phase == Phase.NIGHT | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 92, "start_line": 92, "start_column": 4, "end_column": 50}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-44 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 92 assert not voting_state.players[\"p2\"][\"alive\"] 93 assert voting_state.phase == Phase.NIGHT 94 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 93, "start_line": 93, "start_column": 4, "end_column": 44}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-50 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 99 voting_state.end_voting(\"p2\") 100 assert not voting_state.players[\"p2\"][\"alive\"] 101 assert voting_state.phase == Phase.ENDED | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 100, "start_line": 100, "start_column": 4, "end_column": 50}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-44 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 100 assert not voting_state.players[\"p2\"][\"alive\"] 101 assert voting_state.phase == Phase.ENDED 102 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 101, "start_line": 101, "start_column": 4, "end_column": 44}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-15 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 106 over = game_state.check_game_over() 107 assert over 108 assert game_state.winner == GameWinner.INNOCENT | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 107, "start_line": 107, "start_column": 4, "end_column": 15}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-51 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 107 assert over 108 assert game_state.winner == GameWinner.INNOCENT 109 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 108, "start_line": 108, "start_column": 4, "end_column": 51}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-15 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 114 over = game_state.check_game_over() 115 assert over 116 assert game_state.winner == GameWinner.MAFIA | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 115, "start_line": 115, "start_column": 4, "end_column": 15}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-48 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 115 assert over 116 assert game_state.winner == GameWinner.MAFIA 117 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 116, "start_line": 116, "start_column": 4, "end_column": 48}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-15 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 120 over = game_state.check_game_over() 121 assert over 122 assert game_state.winner == GameWinner.DRAW | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 121, "start_line": 121, "start_column": 4, "end_column": 15}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-47 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 121 assert over 122 assert game_state.winner == GameWinner.DRAW 123 | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 122, "start_line": 122, "start_column": 4, "end_column": 47}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-19 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 129 over = game_state.check_game_over() 130 assert not over 131 assert game_state.winner is None | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 130, "start_line": 130, "start_column": 4, "end_column": 19}, {"category": "Security", "severity": "Low", "tool": "bandit", "title": "B101: Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "description": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code. | Test: assert_used | Confidence: HIGH, Severity: LOW | Columns: 4-36 | CWE-703: https://cwe.mitre.org/data/definitions/703.html | Code: 130 assert not over 131 assert game_state.winner is None | More info: https://bandit.readthedocs.io/en/1.7.5/plugins/b101_assert_used.html", "file_path": "./backend/tests/unit/domain/test_game_state.py", "line_number": 131, "start_line": 131, "start_column": 4, "end_column": 36}]}