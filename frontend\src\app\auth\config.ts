import { z } from "zod";

const configSchema = z.object({
  projectId: z.string().default(""),
  jwksUrl: z.string().default(""),
  publishableClientKey: z.string().default(""),
  handlerUrl: z.string().default('auth')
});

type StackAuthExtensionConfig = z.infer<typeof configSchema>;

declare const __STACK_AUTH_CONFIG__: string;

// Debug: Log the configuration being loaded
console.log('Stack Auth Config Raw:', __STACK_AUTH_CONFIG__);

let parsedConfig;
try {
  parsedConfig = JSON.parse(__STACK_AUTH_CONFIG__);
  console.log('Stack Auth Config Parsed:', parsedConfig);
} catch (error) {
  console.error('Failed to parse Stack Auth config, using fallback:', error);
  // Fallback configuration
  parsedConfig = {
    projectId: "97bafa9b-894f-4d26-9dd4-6f0f69c41954",
    publishableClientKey: "pck_qz8nx0vvfaadked7dkqzg4whks8gzga6bb6txf10m9z4r",
    jwksUrl: "https://api.stack-auth.com/api/v1/projects/97bafa9b-894f-4d26-9dd4-6f0f69c41954/.well-known/jwks.json",
    handlerUrl: "auth"
  };
}

export const config: StackAuthExtensionConfig = configSchema.parse(parsedConfig);

