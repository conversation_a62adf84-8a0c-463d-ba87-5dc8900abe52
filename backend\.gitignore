# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
env/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# FastAPI
.pytest_cache/
coverage.xml
.coverage

# Uvicorn
*.log

# Analysis 
analysis_reports/
test_analysis_report.json
quick_analysis_report.json
quick_test.py

