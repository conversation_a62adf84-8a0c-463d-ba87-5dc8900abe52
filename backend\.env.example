# Archon Backend Environment Variables
# Copy this file to .env and fill in your values

# Database Configuration
# For Docker: use service names (postgres, redis)
# For local: use localhost
DATABASE_URL_DEV=******************************************************/archon_dev
DATABASE_URL_ADMIN_DEV=******************************************************/archon_dev
DATABASE_URL_PROD=******************************************************/archon_prod
DATABASE_URL_ADMIN_PROD=******************************************************/archon_prod



# GitHub OAuth Configuration
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Firebase Authentication (optional)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id
FIREBASE_PRIVATE_KEY="-----B<PERSON>IN PRIVATE KEY-----\nyour_firebase_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
FIREBASE_CLIENT_ID=your_firebase_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Stack Auth Configuration (optional)
STACK_AUTH_PROJECT_ID=your_stack_auth_project_id
STACK_AUTH_PUBLISHABLE_CLIENT_KEY=your_stack_auth_publishable_key
STACK_AUTH_SECRET_SERVER_KEY=your_stack_auth_secret_key

# Application Configuration
ARCHON_SERVICE_TYPE=development
APP_ENV=production  # Set to 'production' for production deployment
DEBUG=true

# Production Configuration
PRODUCTION_DOMAIN=your-domain.com

# GitHub OAuth Configuration (required for production)
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_REDIRECT_URI=https://your-domain.com/routes/github/callback

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here_make_it_long_and_random

# External APIs
OPENAI_API_KEY=your_openai_api_key_if_needed

# Logging
LOG_LEVEL=INFO
